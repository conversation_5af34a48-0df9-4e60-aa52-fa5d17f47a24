"""
RE Actor - 对应LabVIEW中的RE ActorFrame
管理RE相关的全局变量和悬停逻辑
"""

class REActor:
    def __init__(self):
        """初始化RE Actor的内部全局变量"""
        # 对应LabVIEW Actor中的Global变量
        self.Hovered = False
        
        # 其他RE相关的状态变量（根据需要扩展）
        self.initialized = False
        
        print("REActor 初始化完成")
    
    def hover(self, status):
        """
        RE悬停逻辑
        TODO: 实现具体的RE悬停算法
        """
        try:
            # 占位符实现
            self.Hovered = True
            
            # 同步到status对象（保持兼容性）
            status.flow.hover.reHovered = self.Hovered
            
            print(f"REActor.hover: RE悬停完成, Hovered={self.Hovered}")
            
        except Exception as e:
            print(f"REActor.hover 执行失败: {e}")
    
    def reset_hover_state(self):
        """重置悬停状态"""
        self.Hovered = False
        print("REActor: 悬停状态已重置")
    
    def get_hover_state(self):
        """获取悬停状态"""
        return self.Hovered
    
    def initialize(self):
        """初始化RE Actor"""
        try:
            self.initialized = True
            self.reset_hover_state()
            print("REActor: 初始化完成")
            
        except Exception as e:
            print(f"REActor 初始化失败: {e}")
    
    def shutdown(self):
        """关闭RE Actor"""
        try:
            self.reset_hover_state()
            self.initialized = False
            print("REActor: 已关闭")
            
        except Exception as e:
            print(f"REActor 关闭失败: {e}")
