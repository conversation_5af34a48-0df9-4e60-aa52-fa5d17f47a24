"""
Piezo Actor - 对应LabVIEW中的Piezo ActorFrame
管理Piezo相关的全局变量和悬停逻辑
"""

class PiezoActor:
    def __init__(self):
        """初始化Piezo Actor的内部全局变量"""
        # 对应LabVIEW Actor中的Global变量
        self.Hovered = False
        
        # 其他Piezo相关的状态变量（根据需要扩展）
        self.initialized = False
        
        print("PiezoActor 初始化完成")
    
    def hover(self, status):
        """
        Piezo悬停逻辑
        TODO: 实现具体的Piezo悬停算法
        """
        try:
            # 占位符实现
            self.Hovered = True
            
            # 同步到status对象（保持兼容性）
            status.flow.hover.piezoHovered = self.Hovered
            
            print(f"PiezoActor.hover: Piezo悬停完成, Hovered={self.Hovered}")
            
        except Exception as e:
            print(f"PiezoActor.hover 执行失败: {e}")
    
    def reset_hover_state(self):
        """重置悬停状态"""
        self.Hovered = False
        print("PiezoActor: 悬停状态已重置")
    
    def get_hover_state(self):
        """获取悬停状态"""
        return self.Hovered
    
    def initialize(self):
        """初始化Piezo Actor"""
        try:
            self.initialized = True
            self.reset_hover_state()
            print("PiezoActor: 初始化完成")
            
        except Exception as e:
            print(f"PiezoActor 初始化失败: {e}")
    
    def shutdown(self):
        """关闭Piezo Actor"""
        try:
            self.reset_hover_state()
            self.initialized = False
            print("PiezoActor: 已关闭")
            
        except Exception as e:
            print(f"PiezoActor 关闭失败: {e}")
