
from enum import Enum
from dataclasses import dataclass, field

# 数据类集中存放在这
# ------ 枚举类型 -------
# 包含三个因素 模式 状态 标志位 划分出这三个就可以进行任意位置的逻辑编写
# 顶层状态，运行模式
class MotorMode(Enum):
    MANUAL = "Manual"
    STM_01_PIEZO = "Piezo"
    STM_02_MOTOR = "Motor"
    STM_03_MOTOR_PIEZO = "Piezo+Motor"

# 子状态，电导判断
class MotorFlowStep(Enum):
    LOGG_DOWNWARD = "LogG_Downward"
    LOGG_UPWARD = "LogG_Upward"
    LOGG_EXCEED_UPPER = "LogG_Exceed_Upper"
    LOGG_EXCEED_LOWER = "LogG_Exceed_Lower"
    IDLE = "Idle"

class HoverSate(Enum):
    INITIALIZE = "00.00 - Initialize" # 状态初始化
    ENABLE = "10.00 - Enable?" # 启用检查
    COUNT = "20.00 - Count" # 计数
    HOVER = "30.00 - Hover" # 悬停进行中
    DONE_CHECK = "40.00 - Done?" # 完成检查
    RETURN = "50.00 - Return" # 返回
    END = "60.00 - End" # 结束

# ------ 数据类 -------
@dataclass
class HoverStep:
    State: HoverSate = HoverSate.INITIALIZE
    Done: bool = False
    Count: int = 0
    Busy: bool = False

@dataclass
class Controller:
    ID: int = 0
    MotorPosition_Step: int = 0
    DUT_Temperature: float = 0.0
    Ambient_Temperature: float = 0.0
    Bias: int = 0
    Piezo: int = 0
    RE: int = 0
    CE: int = 0
    dT: int = 0
    AI0: int = 0
    AI1: int = 0
    Current: int = 0
    LOGG: int = 0
    MotorPosition_um: int = 0
    PiezoPosition_um: int = 0

@dataclass
class Record:
    State: str = ""
    ref: str = ""
    Counter: int = 0
    FileOpen: bool = False
    FixedFileName: str = ""
    FileIndex: int = 0

@dataclass
class Flow:
    Mode: MotorMode = MotorMode.MANUAL
    State: MotorFlowStep = MotorFlowStep.IDLE
    LogG_Exceed_Lower: bool = False
    LogG_Exceed_Upper: bool = False
    Piezo_Exceed_Lower: bool = False
    Piezo_Exceed_Upper: bool = False
    StepInPos: bool = False
    Motor_Step_Tick: int = 0
    Hover: HoverStep = field(default_factory=HoverStep)
    LogG_cmd_Tick: int = 0
    LogG_cmd_Motor: int = 0
    LogG_cmd_Piezo: int = 0
    Piezo_EU_DOff: bool = False

@dataclass
class Status:
    controller: Controller = field(default_factory=Controller)
    recorder: Recorder = field(default_factory=Recorder)
    flow: Flow = field(default_factory=Flow)