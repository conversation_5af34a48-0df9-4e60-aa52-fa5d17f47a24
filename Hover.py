import settings
@staticmethod
def Hover_Action(status:Status):
    """悬停动作处理"""
    # TODO: 实现悬停逻辑
    pass

@staticmethod
def Enable(status:Status):
    """启用悬停"""
    if settings.Flow.Piezo.Hover.Enable and not Status.Flow.Hover.Done:
        status.flow.Hover.State = HoverSate.Count
    else:
        status.flow.Hover.State = HoverSate.Enable
    pass

@staticmethod
def Count(status:Status):
    if status.Controller.get_data_series('LOGG')[-10:] == [0] * 10:
        
    Status.Flow.Hover.Count += 1