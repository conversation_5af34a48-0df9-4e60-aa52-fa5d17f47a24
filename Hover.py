from settings import settings
from HDcontroll import Motor_Stop
from PiezoActor import PiezoActor
from BiasActor import BiasActor
from REActor import REActor

class HoverManager:
    """
    悬停管理器 - 管理三个Actor实例
    对应LabVIEW中的ActorFrame架构
    """
    def __init__(self):
        """初始化三个Actor实例"""
        self.piezo_actor = PiezoActor()
        self.bias_actor = BiasActor()
        self.re_actor = REActor()

        print("HoverManager: 三个Actor初始化完成")

    def initialize_all_actors(self):
        """初始化所有Actor"""
        self.piezo_actor.initialize()
        self.bias_actor.initialize()
        self.re_actor.initialize()
        print("HoverManager: 所有Actor初始化完成")

    def shutdown_all_actors(self):
        """关闭所有Actor"""
        self.piezo_actor.shutdown()
        self.bias_actor.shutdown()
        self.re_actor.shutdown()
        print("HoverManager: 所有Actor已关闭")

    def reset_all_hover_states(self):
        """重置所有Actor的悬停状态"""
        self.piezo_actor.reset_hover_state()
        self.bias_actor.reset_hover_state()
        self.re_actor.reset_hover_state()
        print("HoverManager: 所有悬停状态已重置")

    def get_all_hover_states(self):
        """获取所有Actor的悬停状态"""
        return {
            'piezo': self.piezo_actor.get_hover_state(),
            'bias': self.bias_actor.get_hover_state(),
            're': self.re_actor.get_hover_state()
        }

    def hover_all(self, status):
        """调用所有Actor的悬停方法"""
        try:
            self.piezo_actor.hover(status)
            self.bias_actor.hover(status)
            self.re_actor.hover(status)

            print("HoverManager: 所有Actor悬停操作完成")

        except Exception as e:
            print(f"HoverManager.hover_all 执行失败: {e}")

# 创建全局HoverManager实例
hover_manager = HoverManager()
@staticmethod
def Hover_Action(status:Status):
    """悬停动作处理"""
    # TODO: 实现悬停逻辑
    pass

@staticmethod
def Initialize(status:Status):
    """初始化悬停状态"""
    status.flow.hover.State = HoverSate.ENABLE
    status.flow.hover.Done = False
    status.flow.hover.Count = 0
    status.flow.hover.Busy = False
    pass

@staticmethod
def Enable(status:Status):
    """启用悬停"""
    if settings.Flow.Piezo.Hover.Enable and not Status.Flow.Hover.Done:
        status.flow.Hover.State = HoverSate.Count
    else:
        status.flow.Hover.State = HoverSate.Enable
    pass

@staticmethod
def Count(status: Status):
    """
    计数电导值在设定范围内的个数，并根据敏感度阈值切换悬停状态
    """
    try:
        from settings import settings

        # 获取电导数据序列
        logg_series = status.controller.get_data_series('LOGG')
        if not logg_series:
            return

        # 获取悬停设置的上下限
        logg_upper = settings.data['Flow']['Piezo']['Hover']['Log(G/G0)+']
        logg_lower = settings.data['Flow']['Piezo']['Hover']['Log(G/G0)-']

        # 统计在范围内的电导值个数
        count_sum = 0
        for logg_value in logg_series:
            if logg_lower <= logg_value <= logg_upper:
                count_sum += 1

        # 累加到状态中的计数器
        status.flow.hover.Count += count_sum

        # 获取敏感度阈值
        sensitivity = settings.data['Flow']['Piezo']['Hover']['Sensitivity']

        # 根据累计计数判断是否切换到悬停状态
        if status.flow.hover.Count >= sensitivity:
            # 达到敏感度阈值，切换到悬停状态
            status.flow.hover.state = HoverSate.Hover
            print(f"电导计数达到阈值 {sensitivity}，切换到悬停状态")
        else:
            # 未达到阈值，继续计数状态
            status.flow.hover.state = HoverSate.Count
            print(f"电导计数: {status.flow.hover.Count}/{sensitivity}，继续计数")

    except KeyError as e:
        print(f"获取悬停设置失败: {e}")
    except Exception as e:
        print(f"Count函数执行失败: {e}")

    @staticmethod
    def Hover(status: Status):
        """
        悬停主函数，调用Bias、Piezo、RE的悬停函数
        """
        try:
            # 设置悬停状态
            status.flow.hover.State = HoverSate.DONE
            status.flow.hover.Busy = True

            # 使用HoverManager调用所有Actor的悬停方法
            hover_manager.hover_all(status)

            print("悬停操作完成，通过HoverManager调用了所有Actor")

        except Exception as e:
            print(f"Hover函数执行失败: {e}")

