from settings import settings
from HDcontroll import Motor_Stop
@staticmethod
def Hover_Action(status:Status):
    """悬停动作处理"""
    # TODO: 实现悬停逻辑
    pass

@staticmethod
def Enable(status:Status):
    """启用悬停"""
    if settings.Flow.Piezo.Hover.Enable and not Status.Flow.Hover.Done:
        status.flow.Hover.State = HoverSate.Count
    else:
        status.flow.Hover.State = HoverSate.Enable
    pass

@staticmethod
def Count(status: Status):
    """
    计数电导值在设定范围内的个数，并根据敏感度阈值切换悬停状态
    """
    try:
        from settings import settings

        # 获取电导数据序列
        logg_series = status.controller.get_data_series('LOGG')
        if not logg_series:
            return

        # 获取悬停设置的上下限
        logg_upper = settings.data['Flow']['Piezo']['Hover']['Log(G/G0)+']
        logg_lower = settings.data['Flow']['Piezo']['Hover']['Log(G/G0)-']

        # 统计在范围内的电导值个数
        count_sum = 0
        for logg_value in logg_series:
            if logg_lower <= logg_value <= logg_upper:
                count_sum += 1

        # 累加到状态中的计数器
        status.flow.hover.Count += count_sum

        # 获取敏感度阈值
        sensitivity = settings.data['Flow']['Piezo']['Hover']['Sensitivity']

        # 根据累计计数判断是否切换到悬停状态
        if status.flow.hover.Count >= sensitivity:
            # 达到敏感度阈值，切换到悬停状态
            status.flow.hover.state = HoverSate.Hover
            print(f"电导计数达到阈值 {sensitivity}，切换到悬停状态")
        else:
            # 未达到阈值，继续计数状态
            status.flow.hover.state = HoverSate.Count
            print(f"电导计数: {status.flow.hover.Count}/{sensitivity}，继续计数")

    except KeyError as e:
        print(f"获取悬停设置失败: {e}")
    except Exception as e:
        print(f"Count函数执行失败: {e}")

    @staticmethod
    def Hover(status: Status):
        status.flow.hover.state = HoverSate.Done
        status.flow.hover.busy = True
        # HoverInit vi中 原actroframe结构