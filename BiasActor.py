"""
Bias Actor - 对应LabVIEW中的Bias ActorFrame
管理Bias相关的全局变量和悬停逻辑
"""

class BiasActor:
    def __init__(self):
        """初始化Bias Actor的内部全局变量"""
        # 对应LabVIEW Actor中的Global变量
        self.Hovered = False
        self.Cut_Bias = False  # 根据你提到的Cut_Bias变量
        
        # 其他Bias相关的状态变量（根据需要扩展）
        self.initialized = False
        
        print("BiasActor 初始化完成")
    
    def hover(self, status):
        """
        Bias悬停逻辑
        TODO: 实现具体的Bias悬停算法
        """
        try:
            # 占位符实现
            self.Hovered = True
            
            # 同步到status对象（保持兼容性）
            status.flow.hover.biasHovered = self.Hovered
            
            print(f"BiasActor.hover: Bias悬停完成, Hovered={self.Hovered}")
            
        except Exception as e:
            print(f"BiasActor.hover 执行失败: {e}")
    
    def cut_bias(self):
        """
        切断Bias功能
        TODO: 实现具体的Cut_Bias逻辑
        """
        try:
            self.Cut_Bias = True
            print(f"BiasActor: Cut_Bias设置为 {self.Cut_Bias}")
            
        except Exception as e:
            print(f"BiasActor.cut_bias 执行失败: {e}")
    
    def reset_hover_state(self):
        """重置悬停状态"""
        self.Hovered = False
        self.Cut_Bias = False
        print("BiasActor: 悬停状态已重置")
    
    def get_hover_state(self):
        """获取悬停状态"""
        return self.Hovered
    
    def get_cut_bias_state(self):
        """获取Cut_Bias状态"""
        return self.Cut_Bias
    
    def initialize(self):
        """初始化Bias Actor"""
        try:
            self.initialized = True
            self.reset_hover_state()
            print("BiasActor: 初始化完成")
            
        except Exception as e:
            print(f"BiasActor 初始化失败: {e}")
    
    def shutdown(self):
        """关闭Bias Actor"""
        try:
            self.reset_hover_state()
            self.initialized = False
            print("BiasActor: 已关闭")
            
        except Exception as e:
            print(f"BiasActor 关闭失败: {e}")
