import threading
from PyQt6.QtWidgets import (QDialog, QLineEdit, QFormLayout,
                           QDialogButtonBox, QLabel, QMessageBox,
                           QTabWidget, QWidget, QVBoxLayout, QGroupBox,
                           QComboBox, QPushButton, QHBoxLayout)
from PyQt6.QtCore import pyqtSlot, pyqtSignal, Qt

class SettingsDialog(QDialog):
    # 信号：设置改变时发出
    settings_changed = pyqtSignal(dict)
    
    def __init__(self, parent=None, instrument=None, config=None):
        super().__init__(parent)
        self.instrument = instrument
        self.config = config
        
        self.setWindowTitle("控制器设置")
        self.resize(700, 600)  # 增大对话框尺寸以适应新的选项卡
        
        # 创建选项卡小部件
        self.tabs = QTabWidget()
        
        # 创建基本设置选项卡
        self.basic_tab = QWidget()
        self.create_basic_tab()
        
        # 创建放大器设置选项卡
        self.amplifier_tab = QWidget()
        self.create_amplifier_tab()
        
        # 创建Motor设置选项卡
        self.motor_tab = QWidget()
        self.create_motor_tab()

        # 创建Bias设置选项卡
        self.bias_tab = QWidget()
        self.create_bias_tab()

        # 创建Piezo设置选项卡
        self.piezo_tab = QWidget()
        self.create_piezo_tab()

        # 创建Scan设置选项卡
        self.scan_tab = QWidget()
        self.create_scan_tab()

        # 创建General设置选项卡
        self.general_tab = QWidget()
        self.create_general_tab()
        
        
        # 添加选项卡
        self.tabs.addTab(self.basic_tab, "基本设置")
        self.tabs.addTab(self.amplifier_tab, "放大器设置")
        self.tabs.addTab(self.motor_tab, "Motor")
        self.tabs.addTab(self.bias_tab, "Bias")
        self.tabs.addTab(self.piezo_tab, "Piezo")
        self.tabs.addTab(self.scan_tab, "Scan")
        self.tabs.addTab(self.general_tab, "General")
        
        # 主布局
        main_layout = QVBoxLayout()
        main_layout.addWidget(self.tabs)
        
        # 按钮
        button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok | 
                                     QDialogButtonBox.StandardButton.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        main_layout.addWidget(button_box)
        
        # 状态标签
        self.status_label = QLabel("准备就绪")
        main_layout.addWidget(self.status_label)
        
        self.setLayout(main_layout)
    
    def create_basic_tab(self):
        """创建基本设置选项卡"""
        layout = QFormLayout()
        
        # Bias 输入框
        self.bias_input = QLineEdit()
        self.bias_input.setPlaceholderText("请输入Bias电压值(V)")

        # 从settings获取bias值并转换为伏特显示
        try:
            from settings import settings
            bias_uv = settings.data.Controller.Bias.Voltage.value
            bias_v = bias_uv / 1e6  # 转换为伏特
        except (KeyError, AttributeError):
            bias_v = 0.1  # 默认值

        self.bias_input.setText(str(bias_v))
        self.bias_input.returnPressed.connect(self.apply_bias)

        layout.addRow("Bias电压(V):", self.bias_input)
        
        # 放大器状态下拉选择框
        self.amp_state_combo = QComboBox()
        self.amp_state_combo.addItems(["Open", "Linear", "Logarithmic"])
        current_state = self.config.get('amp_state', "Logarithmic")
        self.amp_state_combo.setCurrentText(current_state)
        self.amp_state_combo.currentTextChanged.connect(self.amp_state_changed)
        
        layout.addRow("放大器状态:", self.amp_state_combo)
        
        self.basic_tab.setLayout(layout)
    
    def create_amplifier_tab(self):
        """创建放大器设置选项卡"""
        layout = QVBoxLayout()
        
        # 对数放大器设置
        log_group = QGroupBox("对数放大器")
        log_layout = QFormLayout()
        
        # 获取对数放大器设置
        log_settings = self.config.get("amplifier", {}).get("logarithmic", {})
        
        self.a0_input = QLineEdit()
        self.a0_input.setPlaceholderText("请输入a0值")
        self.a0_input.setText(str(log_settings.get("a0", 3.6095)))
        log_layout.addRow("a0:", self.a0_input)
        
        self.b0_input = QLineEdit()
        self.b0_input.setPlaceholderText("请输入b0值")
        self.b0_input.setText(str(log_settings.get("b0", -14.189)))
        log_layout.addRow("b0:", self.b0_input)
        
        self.a1_input = QLineEdit()
        self.a1_input.setPlaceholderText("请输入a1值")
        self.a1_input.setText(str(log_settings.get("a1", -3.5878)))
        log_layout.addRow("a1:", self.a1_input)
        
        self.b1_input = QLineEdit()
        self.b1_input.setPlaceholderText("请输入b1值")
        self.b1_input.setText(str(log_settings.get("b1", -14.166)))
        log_layout.addRow("b1:", self.b1_input)
        
        log_group.setLayout(log_layout)
        layout.addWidget(log_group)
        
        # 线性放大器设置
        linear_group = QGroupBox("线性放大器")
        linear_layout = QFormLayout()
        
        # 获取线性放大器设置
        linear_settings = self.config.get("amplifier", {}).get("linear", {})
        
        self.a_input = QLineEdit()
        self.a_input.setPlaceholderText("请输入a值")
        self.a_input.setText(str(linear_settings.get("a", 1e+9)))
        linear_layout.addRow("a:", self.a_input)
        
        self.b_input = QLineEdit()
        self.b_input.setPlaceholderText("请输入b值")
        self.b_input.setText(str(linear_settings.get("b", 0)))
        linear_layout.addRow("b:", self.b_input)
        
        linear_group.setLayout(linear_layout)
        layout.addWidget(linear_group)
        
        self.amplifier_tab.setLayout(layout)
    
    def create_motor_tab(self):
        """创建Motor设置选项卡"""
        layout = QVBoxLayout()

        # 电机步进控制组
        step_group = QGroupBox("电机步进控制")
        step_layout = QFormLayout()

        # 步进位置输入框
        self.step_value_input = QLineEdit()
        self.step_value_input.setPlaceholderText("请输入步进到的位置")
        self.step_value_input.setText("0")  # 默认值
        step_layout.addRow("步进位置:", self.step_value_input)

        # 按钮布局
        button_layout = QHBoxLayout()

        # 开始步进按钮
        self.start_step_button = QPushButton("开始步进")
        self.start_step_button.clicked.connect(self.start_motor_step)
        button_layout.addWidget(self.start_step_button)

        # 停止按钮
        self.stop_motor_button = QPushButton("停止电机")
        self.stop_motor_button.clicked.connect(self.stop_motor)
        button_layout.addWidget(self.stop_motor_button)

        step_layout.addRow("控制:", button_layout)

        step_group.setLayout(step_layout)
        layout.addWidget(step_group)

        # 单步移动控制组
        single_step_group = QGroupBox("单步移动控制")
        single_step_layout = QFormLayout()

        # 单步移动按钮布局
        single_step_button_layout = QHBoxLayout()

        # 向上一步按钮
        self.step_up_button = QPushButton("向上一步")
        self.step_up_button.clicked.connect(self.step_motor_up)
        single_step_button_layout.addWidget(self.step_up_button)

        # 向下一步按钮
        self.step_down_button = QPushButton("向下一步")
        self.step_down_button.clicked.connect(self.step_motor_down)
        single_step_button_layout.addWidget(self.step_down_button)

        single_step_layout.addRow("单步控制:", single_step_button_layout)

        single_step_group.setLayout(single_step_layout)
        layout.addWidget(single_step_group)

        # 电机PWM控制组
        motor_pwm_group = QGroupBox("电机PWM控制")
        motor_pwm_layout = QFormLayout()

        # PWM周期输入框
        self.pwm_period_input = QLineEdit()
        self.pwm_period_input.setPlaceholderText("请输入PWM周期")
        self.pwm_period_input.setText("10")  # 默认值
        motor_pwm_layout.addRow("PWM周期:", self.pwm_period_input)

        self.pwm_period_input.returnPressed.connect(self.pwmchanged)
        
        motor_pwm_group.setLayout(motor_pwm_layout)
        layout.addWidget(motor_pwm_group)

        # 添加弹性空间
        layout.addStretch()

        self.motor_tab.setLayout(layout)
        
    def pwmchanged(self):
        try:
            pwm_text = self.pwm_period_input.text().strip()
            if not pwm_text:
                self.status_label.setText("请输入有效的PWM周期")
                return

            pwm_period = int(pwm_text)
            print(f"pwm周期已更改为: {pwm_period}")
            # self.config['pwm_period'] = pwm_period
        except ValueError as e:
            self.status_label.setText(f"值格式错误: {str(e)}")
        pass
    def create_bias_tab(self):
        """创建Bias设置选项卡"""
        layout = QFormLayout()
        
        self.bias_tab.setLayout(layout)

    def create_piezo_tab(self):
        """创建Piezo设置选项卡"""
        layout = QVBoxLayout()

        # Piezo 扫描限制设置组
        piezo_limit_group = QGroupBox("Piezo Scan Limit")
        piezo_limit_layout = QFormLayout()

        # 获取当前的Piezo扫描限制值（需要从微伏转换为伏特）
        try:
            from settings import settings
            upper_uv = settings.data.Controller.Piezo.SingleScanUpper.value
            lower_uv = settings.data.Controller.Piezo.SingleScanLower.value
            upper_v = upper_uv / 1e6  # 转换为伏特
            lower_v = lower_uv / 1e6  # 转换为伏特
        except (KeyError, AttributeError):
            upper_v = 1.0
            lower_v = -1.0

        # Upper(V) 输入框
        self.piezo_upper_input = QLineEdit()
        self.piezo_upper_input.setPlaceholderText("请输入Piezo上限电压(V)")
        self.piezo_upper_input.setText(str(upper_v))
        self.piezo_upper_input.textChanged.connect(self.update_piezo_limits)
        piezo_limit_layout.addRow("Upper(V):", self.piezo_upper_input)

        # Lower(V) 输入框
        self.piezo_lower_input = QLineEdit()
        self.piezo_lower_input.setPlaceholderText("请输入Piezo下限电压(V)")
        self.piezo_lower_input.setText(str(lower_v))
        self.piezo_lower_input.textChanged.connect(self.update_piezo_limits)
        piezo_limit_layout.addRow("Lower(V):", self.piezo_lower_input)

        piezo_limit_group.setLayout(piezo_limit_layout)
        layout.addWidget(piezo_limit_group)

        # 添加弹性空间
        layout.addStretch()

        self.piezo_tab.setLayout(layout)

    def create_scan_tab(self):
        """创建Scan设置选项卡"""
        layout = QVBoxLayout()

        # Log(G/G0) 限制设置组
        logg_limit_group = QGroupBox("Log(G/G0) Limit")
        logg_limit_layout = QFormLayout()

        # 获取当前的Log(G/G0)限制值
        try:
            from settings import settings
            logg_positive = settings.data['Flow']['Scan']['Log(G/G0)_positive']
            logg_negative = settings.data['Flow']['Scan']['Log(G/G0)_negative']
        except (KeyError, AttributeError):
            logg_positive = 1.2
            logg_negative = -5

        # Log(G/G0)+ 输入框
        self.logg_positive_input = QLineEdit()
        self.logg_positive_input.setPlaceholderText("请输入Log(G/G0)正限制值")
        self.logg_positive_input.setText(str(logg_positive))
        self.logg_positive_input.returnPressed.connect(self.update_logg_limits)
        logg_limit_layout.addRow("Log(G/G0)+:", self.logg_positive_input)

        # Log(G/G0)- 输入框
        self.logg_negative_input = QLineEdit()
        self.logg_negative_input.setPlaceholderText("请输入Log(G/G0)负限制值")
        self.logg_negative_input.setText(str(logg_negative))
        self.logg_negative_input.returnPressed.connect(self.update_logg_limits)
        logg_limit_layout.addRow("Log(G/G0)-:", self.logg_negative_input)

        logg_limit_group.setLayout(logg_limit_layout)
        layout.addWidget(logg_limit_group)

        # 添加弹性空间
        layout.addStretch()

        self.scan_tab.setLayout(layout)
        
        
    def create_general_tab(self):
        """创建General设置选项卡"""
        layout = QFormLayout()
        
        self.general_tab.setLayout(layout)

    @pyqtSlot()
    def apply_bias(self):
        """应用新的Bias值"""
        if not self.instrument:
            QMessageBox.warning(self, "错误", "未连接串口，无法设置")
            return

        try:
            bias_text = self.bias_input.text().strip()
            if not bias_text:
                self.status_label.setText("请输入有效的Bias电压值")
                return

            # 将输入的伏特值转换为微伏
            try:
                bias_v = float(bias_text)  # 输入的伏特值
                bias_uv = int(bias_v * 1e6)  # 转换为微伏
            except ValueError:
                self.status_label.setText("请输入有效的电压数值")
                return

            # 更新settings中的Controller设置
            from settings import settings
            settings.data.Controller.Bias.Voltage.value = bias_uv

            # 调用差异写入
            success = settings.controllerWrite()
            if success:
                self.status_label.setText(f"Bias电压设置成功: {bias_v}V")
            else:
                self.status_label.setText(f"Bias电压设置失败: {bias_v}V")

        except Exception as e:
            self.status_label.setText(f"设置出错: {str(e)[:30]}")
    
    @pyqtSlot(str)
    def amp_state_changed(self, new_state):
        """放大器状态更改处理"""
        if not self.instrument:
            return
            
        self.config['amp_state'] = new_state
        self.status_label.setText(f"放大器状态已更改为: {new_state}")
    

    # TODO: 待修改
    def get_updated_config(self):
        """收集所有输入框的值并返回更新后的配置"""
        try:
            # 基本设置 - bias电压(V转换为μV)
            bias_v = float(self.bias_input.text()) if self.bias_input.text() else 0.1
            bias = int(bias_v * 1e6)  # 转换为微伏
            
            # 获取放大器状态
            amp_state = self.amp_state_combo.currentText()
            
            # 对数放大器设置 
            a0 = float(self.a0_input.text()) if self.a0_input.text() else 3.6095
            b0 = float(self.b0_input.text()) if self.b0_input.text() else -14.189
            a1 = float(self.a1_input.text()) if self.a1_input.text() else -3.5878
            b1 = float(self.b1_input.text()) if self.b1_input.text() else -14.166
            
            # 线性放大器设置
            a = float(self.a_input.text()) if self.a_input.text() else 1e+9
            b = float(self.b_input.text()) if self.b_input.text() else 0
            
            # 创建更新后的配置
            updated_config = {
                'bias': bias,
                'amp_state': amp_state,
                'amplifier': {
                    'logarithmic': {
                        'a0': a0,
                        'b0': b0,
                        'a1': a1,
                        'b1': b1
                    },
                    'linear': {
                        'a': a,
                        'b': b
                    }
                }
            }
            
            return updated_config
        except ValueError as e:
            self.status_label.setText(f"值格式错误: {str(e)}")
            return self.config  # 返回原配置
    
    def accept(self):
        """确认按钮处理"""
        updated_config = self.get_updated_config()
        self.settings_changed.emit(updated_config)
        super().accept()
    
    def get_config(self):
        """返回当前配置"""
        return self.get_updated_config()

    @pyqtSlot()
    def start_motor_step(self):
        """开始电机步进"""
        if not self.instrument:
            QMessageBox.warning(self, "错误", "未连接串口，无法控制电机")
            return

        try:
            step_text = self.step_value_input.text().strip()
            if not step_text:
                self.status_label.setText("请输入有效的步进位置")
                return

            # 转换步进值为整数
            try:
                step_value = int(step_text)
            except ValueError:
                self.status_label.setText("请输入有效的数值")
                return

            # 启动线程发送命令，避免UI阻塞
            threading.Thread(target=self._send_motor_step_command, args=(step_value,)).start()

        except Exception as e:
            self.status_label.setText(f"步进设置出错: {str(e)[:30]}")

    @pyqtSlot()
    def stop_motor(self):
        """停止电机"""
        if not self.instrument:
            QMessageBox.warning(self, "错误", "未连接串口，无法控制电机")
            return

        try:
            # 启动线程发送停止命令
            threading.Thread(target=self._send_motor_stop_command).start()

        except Exception as e:
            self.status_label.setText(f"停止电机出错: {str(e)[:30]}")

    def _send_motor_step_command(self, step_value):
        """在单独线程中发送电机步进命令"""
        try:
            import HDControll

            # 设置步进值 (item=6)
            HDControll.Motor_StepValue(step_value)

            # 开始步进 (item=1, value=3)
            HDControll.Motor_Step()

            # 更新状态
            self.status_label.setText(f"电机步进开始，目标位置: {step_value}")

        except Exception as e:
            self.status_label.setText(f"步进命令发送失败: {str(e)[:30]}")

    def _send_motor_stop_command(self):
        """在单独线程中发送电机停止命令"""
        try:
            import HDControll

            # 停止电机 (item=1, value=0)
            HDControll.Motor_Stop()

            # 更新状态
            self.status_label.setText("电机已停止")

        except Exception as e:
            self.status_label.setText(f"停止命令发送失败: {str(e)[:30]}")

    @pyqtSlot()
    def step_motor_up(self):
        """电机向上一步"""
        if not self.instrument:
            QMessageBox.warning(self, "错误", "未连接串口，无法控制电机")
            return

        try:
            # 获取当前步进位置
            current_step = int(self.step_value_input.text()) if self.step_value_input.text() else 0
            # 向上一步（步数-1）
            target_step = current_step - 1

            # 更新输入框显示
            self.step_value_input.setText(str(target_step))

            # 启动线程发送命令
            threading.Thread(target=self._send_single_step_command, args=(target_step, "向上")).start()

        except Exception as e:
            self.status_label.setText(f"向上步进出错: {str(e)[:30]}")

    @pyqtSlot()
    def step_motor_down(self):
        """电机向下一步"""
        if not self.instrument:
            QMessageBox.warning(self, "错误", "未连接串口，无法控制电机")
            return

        try:
            # 获取当前步进位置
            current_step = int(self.step_value_input.text()) if self.step_value_input.text() else 0
            # 向下一步（步数+1）
            target_step = current_step + 1

            # 更新输入框显示
            self.step_value_input.setText(str(target_step))

            # 启动线程发送命令
            threading.Thread(target=self._send_single_step_command, args=(target_step, "向下")).start()

        except Exception as e:
            self.status_label.setText(f"向下步进出错: {str(e)[:30]}")

    def _send_single_step_command(self, target_step, direction):
        """在单独线程中发送单步移动命令"""
        try:
            import HDControll

            # 设置目标步进值
            HDControll.Motor_StepValue(target_step)

            # 执行步进
            HDControll.Motor_Step()

            # 更新状态
            self.status_label.setText(f"电机{direction}一步，当前位置: {target_step}")

        except Exception as e:
            self.status_label.setText(f"单步移动失败: {str(e)[:30]}")

    def update_logg_limits(self):
        """更新Log(G/G0)限制值到运行时设置"""
        try:
            from settings import settings

            # 获取输入值
            positive_text = self.logg_positive_input.text().strip()
            negative_text = self.logg_negative_input.text().strip()

            if positive_text:
                positive_value = float(positive_text)
                settings.data['Flow']['Scan']['Log(G/G0)_positive'] = positive_value

            if negative_text:
                negative_value = float(negative_text)
                settings.data['Flow']['Scan']['Log(G/G0)_negative'] = negative_value

            # 更新状态
            if positive_text and negative_text:
                self.status_label.setText(f"Log(G/G0)限制已更新: +{positive_value}, -{negative_value}")

        except ValueError:
            self.status_label.setText("Log(G/G0)限制值格式错误")
        except Exception as e:
            self.status_label.setText(f"更新Log(G/G0)限制失败: {str(e)[:30]}")

    def update_piezo_limits(self):
        """更新Piezo扫描限制值到Controller设置并调用差异写入"""
        try:
            from settings import settings

            # 获取输入值
            upper_text = self.piezo_upper_input.text().strip()
            lower_text = self.piezo_lower_input.text().strip()

            if upper_text:
                upper_v = float(upper_text)
                upper_uv = int(upper_v * 1e6)  # 转换为微伏
                settings.data.Controller.Piezo.SingleScanUpper.value = upper_uv

            if lower_text:
                lower_v = float(lower_text)
                lower_uv = int(lower_v * 1e6)  # 转换为微伏
                settings.data.Controller.Piezo.SingleScanLower.value = lower_uv

            # 调用差异写入
            if upper_text or lower_text:
                success = settings.controllerWrite()
                if success:
                    self.status_label.setText(f"Piezo限制已写入控制器: Upper={upper_text}V, Lower={lower_text}V")
                else:
                    self.status_label.setText("Piezo限制写入控制器失败")

        except ValueError:
            self.status_label.setText("Piezo限制值格式错误")
        except Exception as e:
            self.status_label.setText(f"更新Piezo限制失败: {str(e)[:30]}")


