import struct
import sys
import threading
import queue
import json
import os
import time
import numpy as np
from PyQt6.QtWidgets import (QApplication, QWidget, QTableWidget,
                             QTableWidgetItem, QVBoxLayout, QHBoxLayout,
                             QPushButton, QCheckBox, QMessageBox, QLabel,
                             QDialog,
                             QSplitter, QComboBox)
from PyQt6.QtCore import QTimer, Qt, pyqtSlot

# 导入控制器工具模块
import controllerutil
from controllerutil import (load_config_and_initialize, 
                           process_data, process_received_data, reshape_data)

# 导入设置对话框
from settings_dialog import SettingsDialog
from settings import settings
import HDControll
# 导入绘图模块
from plotgraph import LogGPlot
from Communicator import IControllerCommunicator, SerialCommunicator, TcpCommunicator, DataReaderThread, SerialSignals 
from StateMachine import ModeStateMachine,MotorMode
from Status import Status

# --- 配置常量 ---
# 串口参数（请根据实际情况修改）
SERIAL_RESOURCE_NAME = "USB0::0xFFFF::0xFFFB::2073325E4232::RAW"
# 配置文件路径
SETTINGS_FILE = "settings.json"
# GUI更新频率控制 - 避免高速数据流导致界面卡顿
GUI_UPDATE_INTERVAL_MS = 100  # 每100ms更新一次表格和状态，而不是每帧都更新
PLOT_UPDATE_INTERVAL_MS = 50  # 每50ms更新一次图表，保持波形的流畅性

# --- 主窗口类 ---
class DataWindow(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("控制器接口")

        # 加载设置
        self.load_settings()
        # 字典访问方法 print(settings.data['Flow']['Scan']['Log(G/G0)_positive'] )

        # --- UI元素 ---
        # 电机控制按钮
        self.down_button = QPushButton("电机下降 (Down)")
        self.up_button = QPushButton("电机上升 (Up)")
        self.reverse_checkbox = QCheckBox("反转电机方向")
        
        # 模式选择选择框
        self.mode_checkbox = QComboBox()
        self.mode_checkbox.addItem("Manual")
        self.mode_checkbox.addItem("Piezo")
        self.mode_checkbox.addItem("Motor")
        self.mode_checkbox.addItem("Piezo+Motor")
        # 设置按钮
        self.settings_button = QPushButton("设置")
        self.settings_button.clicked.connect(self.show_settings_dialog)

        # 暂停/继续按钮
        self.pause_button = QPushButton("暂停")
        self.pause_button.clicked.connect(self.toggle_pause)
        self.is_paused = False

        # 记录文件（保存数据流到txt作为调试）
        # self._record_file = open("data_record.txt", "ab")
        # print("SerialCommunicator 实例已创建，开始记录到data_record.txt")
        
        # 状态标签
        self.status_label = QLabel("就绪")

        # 数据表格
        self.table = QTableWidget()
        
        # 创建绘图小部件（配置100000数据点的窗口大小，每次更新）
        self.log_g_plot = LogGPlot(window_size=10000, update_size=200)

        # 创建status对象作为数据容器
        self.status = Status()

        # --- 布局 ---
        # 电机控制布局
        motor_control_layout = QHBoxLayout()
        motor_control_layout.addWidget(self.down_button)
        motor_control_layout.addWidget(self.up_button)
        motor_control_layout.addWidget(self.reverse_checkbox)
        motor_control_layout.addWidget(self.settings_button)
        motor_control_layout.addWidget(self.pause_button)
        motor_control_layout.addWidget(self.status_label)
        motor_control_layout.addWidget(self.mode_checkbox)
        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Vertical)
        
        # 创建表格容器
        table_widget = QWidget()
        table_layout = QVBoxLayout(table_widget)
        table_layout.setContentsMargins(0, 0, 0, 0)
        table_layout.addWidget(self.table)
        
        # 添加表格和绘图到分割器
        splitter.addWidget(table_widget)
        splitter.addWidget(self.log_g_plot)
        
        # 设置分割器比例
        splitter.setSizes([300, 400])  # 表格占1/3，图表占2/3
        
        # 主布局
        self.layout = QVBoxLayout()
        self.layout.addLayout(motor_control_layout)
        self.layout.addWidget(splitter)
        self.setLayout(self.layout)



        # --- 信号连接 ---
        self.up_button.pressed.connect(self.up_button_pressed)
        self.up_button.released.connect(self.stop_button_released)
        self.down_button.pressed.connect(self.down_button_pressed)
        self.down_button.released.connect(self.stop_button_released)
        self.reverse_checkbox.stateChanged.connect(self.reverse_direction_changed)
        self.mode_checkbox.currentTextChanged.connect(self.mode_changed)
        # --- 状态变量 ---
        self.property_names = [
            "ID", "PWMCounter", "TS0", "TS1", "Bias", "Piezo",
            "AO2", "AO3", "dT", "AI0", "AI1"
        ]
        
        # 实际物理参数名称 最终要得到的就这些数据
        self.physical_property_names = [
            "ID", "MotorPosition", "DUT Temperature", "Ambient Temperature",
            "Bias", "Piezo", "RE", "CE", "dT(uSec)", "AI0", "AI1",
            "Log(G/G0)", "Current", "Motor Position (Physical)", "Piezo Position (Physical)"
        ]
        
        self.loop_index = 0
        self.reverse_direction = False  # 初始化电机方向状态
        self.rm = None
        self.communicator: IControllerCommunicator = SerialCommunicator() # 声明类型为抽象接口
        self.data_reader_thread: DataReaderThread = None # 声明类型为新的线程类
        self.serial_thread = None

        # 高速数据处理相关变量
        self.last_gui_update = 0      # 上次GUI更新时间
        self.last_plot_update = 0     # 上次图表更新时间
        self.frame_count = 0          # 接收到的帧数
        self.data_group_count = 0     # 接收到的数据组总数
        self.start_time = None        # 开始时间
        self.latest_processed_data = None  # 最新的处理后数据（用于GUI显示）
        # --- 控制器初始化（仅当串口连接成功时执行） ---
        self.init_controller()
        # 初始化状态机，传入status对象
        self.mode_state_machine = ModeStateMachine(self.status)
        # 设置初始窗口大小，以便更好地显示图表
        self.resize(800, 600)

    def load_settings(self):
        """加载设置文件"""
        try:
            if os.path.exists(SETTINGS_FILE):
                with open(SETTINGS_FILE, 'r') as f:
                    settings_data = json.load(f)
                print(f"从 {SETTINGS_FILE} 加载设置成功")
            else:
                print(f"设置文件 {SETTINGS_FILE} 不存在")
                raise FileNotFoundError(f"设置文件 {SETTINGS_FILE} 不存在")
                
            # 更新全局设置
            settings.load_from_json(settings_data)
            
            # 如果复选框存在，根据设置更新UI
            if hasattr(self, 'reverse_checkbox'):
                try:
                    self.reverse_checkbox.setChecked(settings.data.Motor.Reverse)
                except AttributeError as e:
                    print(f"无法设置电机反转状态: {e}")
                    
        except Exception as e:
            print(f"加载设置文件时出错: {e}")
            import traceback
            traceback.print_exc()
            raise  # 直接抛出异常，不提供默认值


    def show_settings_dialog(self):
        """显示设置对话框"""
        if not self.communicator:
            QMessageBox.warning(self, "错误", "未连接串口，无法进行设置")
            return
        # 准备当前配置 临时配置
        try:
            current_config = {
                'bias': settings.data.Controller.Bias.Voltage.value,
                'amplifier': {
                    'logarithmic': {
                        'a0': settings.data.Amplifier.Logarithmic.a0,
                        'b0': settings.data.Amplifier.Logarithmic.b0,
                        'a1': settings.data.Amplifier.Logarithmic.a1,
                        'b1': settings.data.Amplifier.Logarithmic.b1
                    },
                    'linear': {
                        'a': settings.data.Amplifier.Linear.a,
                        'b': settings.data.Amplifier.Linear.b
                    }
                }
            }
            
            dialog = SettingsDialog(self, self.communicator, current_config)
            dialog.settings_changed.connect(self.update_settings)
            result = dialog.exec()
            
            if result == QDialog.DialogCode.Accepted:
                self.status_label.setText("设置已应用（仅内存）")
                # 配置更新已在update_settings中完成
                # 提交到主设置
                self.save_settings()
            else:
                self.status_label.setText("设置已取消")
                # 取消时重置临时设置
                settings.reset_temp()
        except AttributeError as e:
            QMessageBox.critical(self, "设置错误", f"获取设置时出错: {str(e)}")
            print(f"获取设置时出错: {e}")
            import traceback
            traceback.print_exc()
        except Exception as e:
            QMessageBox.critical(self, "设置错误", f"打开设置对话框时发生错误: {str(e)}")
            print(f"设置对话框错误: {e}")
            import traceback
            traceback.print_exc()

    @pyqtSlot(dict)
    def update_settings(self, new_config):
        """更新设置（临时，不持久化到文件）"""
        try:
            # 更新电机方向反转
            if 'motor_reverse' in new_config:
                settings.update_temp("Motor.Reverse", new_config['motor_reverse'])
                self.reverse_checkbox.setChecked(new_config['motor_reverse'])
                self.reverse_direction = new_config['motor_reverse']
            
            # 更新放大器状态
            if 'amp_state' in new_config:
                settings.update_temp("Controller.AmpState", new_config['amp_state'])
                # 更新放大器开关
                try:
                    switch0, switch1 = controllerutil.AMP_State_to_value(new_config['amp_state'])
                    controllerutil.dwrite(self.communicator, 12, switch0)
                    controllerutil.dwrite(self.communicator, 13, switch1)
                    print(f"放大器模式已切换为: {new_config['amp_state']}")
                except Exception as e:
                    print(f"切换放大器模式时出错: {e}")
            
            # 更新偏置电压
            if 'bias' in new_config:
                settings.update_temp("Controller.Bias.Voltage.value", new_config['bias'])
                # 发送到控制器
                try:
                    bias_item = settings.data.Controller.Bias.Voltage.item
                    controllerutil.dwrite(self.communicator, bias_item, new_config['bias'])
                    print(f"偏置电压已更新: {new_config['bias']}")
                except Exception as e:
                    print(f"更新偏置电压时出错: {e}")
                
            # 更新放大器设置
            if 'amplifier' in new_config:
                amp_config = new_config['amplifier']
                
                # 更新对数放大器设置
                if 'logarithmic' in amp_config:
                    log_config = amp_config['logarithmic']
                    if 'a0' in log_config:
                        settings.update_temp("Controller.Amplifier.Logarithmic.a0", log_config['a0'])
                    if 'b0' in log_config:
                        settings.update_temp("Controller.Amplifier.Logarithmic.b0", log_config['b0'])
                    if 'a1' in log_config:
                        settings.update_temp("Controller.Amplifier.Logarithmic.a1", log_config['a1'])
                    if 'b1' in log_config:
                        settings.update_temp("Controller.Amplifier.Logarithmic.b1", log_config['b1'])
                
                # 更新线性放大器设置
                if 'linear' in amp_config:
                    linear_config = amp_config['linear']
                    if 'a' in linear_config:
                        settings.update_temp("Controller.Amplifier.Linear.a", linear_config['a'])
                    if 'b' in linear_config:
                        settings.update_temp("Controller.Amplifier.Linear.b", linear_config['b'])
            
            print("设置已临时更新（未提交）")
        except Exception as e:
            print(f"更新设置时出错: {e}")
            import traceback
            traceback.print_exc()

    def init_controller(self):
        """初始化控制器连接和相关线程"""
        # 调用 controllerutil 中的初始化函数
        self.communicator, self.data_reader_thread, status_message = HDControll.initialize_controller('Virtual')
        
        if self.communicator and self.data_reader_thread:
            # 连接信号
            self.data_reader_thread.signals.data_ready.connect(self._on_data_received)
            self.data_reader_thread.signals.error.connect(self.handle_serial_error)
            self.data_reader_thread.signals.no_data.connect(self.handle_no_data)

            # 启动线程
            self.data_reader_thread.start()

            # 初始化统计时间
            self.start_time = time.time()

            # 更新状态
            self.status_label.setText(status_message)
        else:
            # 显示错误消息
            QMessageBox.critical(self, "初始化错误", status_message)
            self.status_label.setText(status_message)
            

    @pyqtSlot(object)
    def _on_data_received(self, processed_data_transposed):
        """
        统一的数据处理入口，在数据读取线程发出 data_ready 信号时被调用。
        直接接收处理后的数据，更新表格，更新图表，并驱动状态机。
        优化：控制GUI更新频率以避免高速数据流导致界面卡顿。
        """
        try:
            # 检查数据是否有效
            if processed_data_transposed is None or not processed_data_transposed:
                print("处理后的数据为空或无效，跳过更新。")
                return

            # 更新统计信息
            self.frame_count += 1
            if processed_data_transposed and len(processed_data_transposed) > 0:
                # 假设每帧包含的数据组数等于第一个数据序列的长度
                self.data_group_count += len(processed_data_transposed[0]) if processed_data_transposed[0] else 0

            # 保存最新数据
            self.latest_processed_data = processed_data_transposed

            # 控制GUI更新频率 - 只在指定间隔内更新界面
            current_time = time.time() * 1000  # 转换为毫秒
            should_update_gui = (current_time - self.last_gui_update >= GUI_UPDATE_INTERVAL_MS)

            if should_update_gui:
                # 更新表格显示
                self.update_table(processed_data_transposed)
                self.last_gui_update = current_time

                # 更新状态标签显示统计信息
                if self.start_time:
                    elapsed_time = time.time() - self.start_time
                    fps = self.frame_count / elapsed_time if elapsed_time > 0 else 0
                    status_text = f"帧数:{self.frame_count} 数据组:{self.data_group_count} 帧率:{fps:.1f}fps"
                    self.status_label.setText(status_text)
            elif not hasattr(self, '_initial_update_done'):
                # 确保至少更新一次表格，即使不在更新间隔内
                self.update_table(processed_data_transposed)
                self._initial_update_done = True

            # 提取 Log(G/G0) 和 Piezo 电压以驱动状态机和更新图表
            # 状态机需要实时数据，但图表可以控制更新频率
            try:
                # 检查 processed_data_transposed 是否有足够的元素
                if len(processed_data_transposed) <= 11:
                    print(f"数据列不足，无法提取 Log(G/G0) 或 Piezo。实际列数: {len(processed_data_transposed)}")
                    return

                # 获取所有样本的 Piezo 电压序列和 Log(G/G0) 序列
                piezo_voltage_series = processed_data_transposed[5]  # 索引 5 对应 "Piezo"
                log_g_g0_series = processed_data_transposed[11]      # 索引 11 对应 "Log(G/G0)"
                motor_step = processed_data_transposed[1]      # 索引 1 对应 "MotorStep"

                # 确保序列不为空
                if not piezo_voltage_series or not log_g_g0_series:
                    print("Piezo 或 Log(G/G0) 数据序列为空。")
                    return

                # 获取最新值
                latest_piezo_voltage = piezo_voltage_series[-1]
                latest_log_g_g0_value = log_g_g0_series[-1]
                latest_motor_step_value = motor_step[-1]
                # 确保值是有效的浮点数
                if not isinstance(latest_piezo_voltage, (int, float)) or not np.isfinite(latest_piezo_voltage) or \
                   not isinstance(latest_log_g_g0_value, (int, float)) or not np.isfinite(latest_log_g_g0_value):
                    print("提取到的 Log(G/G0) 或 Piezo 值无效 (非数字或无穷大)。")
                    return

                # 状态机需要实时数据，每次都更新
                self.mode_state_machine.receive_data_value(latest_piezo_voltage, latest_log_g_g0_value,latest_motor_step_value)

                # 图表更新使用独立的更新频率控制，保持波形流畅性
                should_update_plot = (current_time - self.last_plot_update >= PLOT_UPDATE_INTERVAL_MS)
                if should_update_plot and not self.is_paused:
                    # 应用下采样
                    downsampled_logg, downsampled_piezo = self.apply_display_downsampling(
                        list(log_g_g0_series), list(piezo_voltage_series))
                    self.log_g_plot.update_data(downsampled_logg, downsampled_piezo)
                    self.last_plot_update = current_time
                elif not hasattr(self, '_plot_initial_update_done'):
                    # 确保至少更新一次图表
                    print(f"初始图表更新: Piezo数据点数={len(piezo_voltage_series)}, Log(G/G0)数据点数={len(log_g_g0_series)}")
                    downsampled_logg, downsampled_piezo = self.apply_display_downsampling(
                        list(log_g_g0_series), list(piezo_voltage_series))
                    self.log_g_plot.update_data(downsampled_logg, downsampled_piezo)
                    self._plot_initial_update_done = True

            except IndexError as e:
                print(f"数据索引错误，可能数据结构不符合预期: {e}")
                print(f"processed_data_transposed 长度: {len(processed_data_transposed) if processed_data_transposed else 0}")
            except Exception as e:
                print(f"更新图表或驱动状态机时出错: {str(e)}")
                import traceback
                traceback.print_exc()

        except Exception as e:
            print(f"处理数据时发生错误: {e}")
            # 只在GUI更新时间间隔内更新错误状态，避免频繁更新
            current_time = time.time() * 1000
            if current_time - self.last_gui_update >= GUI_UPDATE_INTERVAL_MS:
                self.status_label.setText(f"处理错误: {str(e)[:30]}")
                self.last_gui_update = current_time
            
    def handle_serial_error(self, error_msg):
        """处理串口错误"""
        # 控制错误信息更新频率
        current_time = time.time() * 1000
        if current_time - self.last_gui_update >= GUI_UPDATE_INTERVAL_MS:
            self.status_label.setText(f"错误: {error_msg[:30]}")
            self.last_gui_update = current_time

    def handle_no_data(self):
        """处理没有数据的情况"""
        # 控制无数据状态更新频率
        current_time = time.time() * 1000
        if current_time - self.last_gui_update >= GUI_UPDATE_INTERVAL_MS:
            self.status_label.setText("无数据")
            self.last_gui_update = current_time

    # --- 电机控制事件处理器 ---
    def up_button_pressed(self):
        """电机上升按钮按下事件"""
        if self.communicator:
            self.status_label.setText("电机上升中...")
            if not self.reverse_direction:
                # 如果不反转方向，调用HDControll.Motor_Upward实现电机上升
                self._send_threaded_command(HDControll.Motor_Upward)
            else:
                # 如果反转方向，调用HDControll.Motor_Downward实现“反向的上升”（即实际下降）
                self._send_threaded_command(HDControll.Motor_Downward)
        else:
            QMessageBox.warning(self, "错误", "串口未连接，无法控制电机。")

    def down_button_pressed(self):
        """电机上升按钮按下事件"""
        if self.communicator:
            self.status_label.setText("电机下降中...")
            if not self.reverse_direction:
                # 如果不反转方向，调用HDControll.Motor_Downward实现电机下降
                self._send_threaded_command(HDControll.Motor_Downward)
            else:
                # 如果反转方向，调用HDControll.Motor_Upward实现“反向的下降”（即实际上升）
                self._send_threaded_command(HDControll.Motor_Upward)
        else:
            QMessageBox.warning(self, "错误", "串口未连接，无法控制电机。")

    def stop_button_released(self):
        """按钮释放事件，停止电机"""
        if self.communicator:
            self._send_threaded_command(HDControll.Motor_Stop)
        else:
            QMessageBox.warning(self, "错误", "串口未连接，无法控制电机。")

    def _send_threaded_command(self, command_func):
        """
        在单独线程中执行一个命令函数并更新UI。
        command_func: 要在线程中执行的HDControll函数（例如 HDControll.Motor_Upward）。
        这里每次运行都会创建一个线程 不过因为不常用所以emmm影响不大吧。有时间可以优化弄一个单独的线程类
        """
        def run_command():
            try:
                # 执行传入的HDControll命令函数
                command_func() 
            except Exception as e:
                print(f"发送命令时出错: {e}")
        
        # 启动新线程执行命令
        threading.Thread(target=run_command).start()

    def reverse_direction_changed(self, state):
        """处理电机方向反转复选框状态变化"""
        try:
            self.reverse_direction = bool(state)
            # 仅更新临时设置，不提交
            settings.update_temp("Motor.Reverse", self.reverse_direction)
            print(f"电机方向反转设置已临时更新: {self.reverse_direction}")
            self.status_label.setText(f"电机方向: {'反转' if self.reverse_direction else '正常'}")
        except Exception as e:
            print(f"更新电机方向设置时出错: {e}")
            import traceback
            traceback.print_exc()

    def update_table(self, data): 
        """更新表格数据"""
        # 检查数据是否有效
        if not data or not data[0]:
            print("接收到的数据无效或为空，无法更新表格。")
            return

        self.table.clearContents()

        # 获取原始数据行数 (ID 到 AI1 的属性数量)
        raw_rows = len(self.property_names) # 这是原始的11个属性
        # 获取处理后的物理参数数据行数 (所有14个物理量)
        processed_rows = len(self.physical_property_names) # 这是14个物理量

        num_cols = len(data[0]) + 1  # 数据列+1用于属性名

        self.table.setRowCount(processed_rows) # 这里的行数应该是物理量的总行数
        self.table.setColumnCount(num_cols)

        # 设置表头
        headers = ["属性"] + [f"数据 {i+1}" for i in range(num_cols - 1)]
        self.table.setHorizontalHeaderLabels(headers)

        # 填充所有物理参数数据行 (直接使用 processed_data_transposed)
        for row_idx in range(processed_rows): # 遍历 physical_property_names 的所有索引
            physical_property_name = self.physical_property_names[row_idx]
            item_name = QTableWidgetItem(physical_property_name)
            self.table.setItem(row_idx, 0, item_name) # 属性名放在第一列

            # 遍历每个样本点的数据
            for col_idx in range(len(data[0])):
                # `data` (即 processed_data_transposed) 的 `row_idx` 行就是 `physical_property_name` 对应的序列
                # `data[row_idx][col_idx]` 就是该物理量在 `col_idx` 样本点的值
                if row_idx < len(data) and col_idx < len(data[row_idx]):
                    value = data[row_idx][col_idx]
                    item_value = QTableWidgetItem(str(value))
                    self.table.setItem(row_idx, col_idx + 1, item_value)

        self.table.resizeColumnsToContents() # 调整列宽以适应内容

    def toggle_pause(self):
        """切换暂停/继续状态"""
        self.is_paused = not self.is_paused
        if self.is_paused:
            self.pause_button.setText("继续")
            self.status_label.setText("图表已暂停")
        else:
            self.pause_button.setText("暂停")
            # 状态会在下次数据更新时自动恢复

    def apply_display_downsampling(self, logg_data, piezo_data):
        """应用显示下采样"""
        try:
            # 获取下采样比例
            display_1_n = settings.data.UI.Chart.Display_1_N if 'Display_1_N' in settings.data.UI.Chart._data else 1

            if display_1_n <= 1:
                # 不需要下采样
                return logg_data, piezo_data

            # 确保两个数据列表长度一致
            min_length = min(len(logg_data), len(piezo_data))
            if min_length == 0:
                return [], []

            # 下采样：每N个点取1个
            downsampled_logg = []
            downsampled_piezo = []

            for i in range(0, min_length, display_1_n):
                downsampled_logg.append(logg_data[i])
                downsampled_piezo.append(piezo_data[i])

            return downsampled_logg, downsampled_piezo

        except Exception as e:
            print(f"下采样处理失败: {e}")
            # 出错时返回原始数据
            return logg_data, piezo_data
        
    def save_settings(self):
        """
        将当前设置提交到主设置，但不持久化到文件
        程序退出时不会保存设置，需要手动调用settings.save_to_file保存
        """
        try:
            # 更新全局设置中的值
            settings.update_temp("Motor.Reverse", self.reverse_direction)
            
            # 提交临时设置到主设置
            settings.commit_temp()
            
            print(f"设置已更新（仅内存）")
            return True
        except Exception as e:
            print(f"更新设置时出错: {e}")
            import traceback
            traceback.print_exc()
            return False

    def closeEvent(self, event):
        """程序关闭时的处理"""
        try:

            # 关闭记录文件
            if hasattr(self, '_record_file') and self._record_file:
                self._record_file.close()
                print("数据记录文件已关闭")

            # 停止串口读取线程
            if self.serial_thread:
                self.serial_thread.stop()
                print("串口读取线程已停止")
            
            # 关闭串口连接
            if self.communicator:
                self.communicator.disconnect()
                print("串口连接已关闭")
            
            # 释放visa资源管理器
            if self.rm:
                self.rm.close()
                print("VISA资源管理器已释放")
            
            # 接受关闭事件
            event.accept()
        except Exception as e:
            print(f"关闭程序时出错: {e}")
            event.accept()

    def mode_changed(self, text):
        """模式选择框变化事件"""
        print(f"模式选择框变化: {text}")
        if text == "Piezo":
            self.status_label.setText("Piezo模式")
            new_mode = MotorMode.STM_01_PIEZO
        elif text == "Motor":
            self.status_label.setText("Motor模式")
            new_mode = MotorMode.STM_02_MOTOR
        elif text == "Piezo+Motor":
            self.status_label.setText("Piezo+Motor模式")
            new_mode = MotorMode.STM_03_MOTOR_PIEZO
        elif text == "Manual":
            self.status_label.setText("Manual模式")
            new_mode = MotorMode.MANUAL
        self.mode_state_machine.set_mode(new_mode)

# --- 程序入口 ---
if __name__ == '__main__':

    print("应用程序启动...")
    app = QApplication(sys.argv)
    window = DataWindow()
    window.show()
    sys.exit(app.exec())


